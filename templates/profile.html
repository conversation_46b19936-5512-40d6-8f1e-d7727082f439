{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Profile Header with animated gradient -->
    <div class="relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl overflow-hidden mb-8 shadow-lg transform transition-all duration-300 hover:shadow-xl">
        <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
        <div class="relative p-8">
            <div class="flex flex-col md:flex-row items-center md:items-start gap-6">
                <!-- Avatar -->
                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white text-3xl font-bold shadow-lg transform transition-all duration-300 hover:scale-105">
                    {{ profile_owner.username[:1].upper() }}
                </div>

                <!-- User Info -->
                <div class="flex-1 text-center md:text-left">
                    <h1 class="text-3xl font-bold text-white tracking-tight">{{ profile_owner.username }}</h1>
                    <div class="mt-2 flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
                        <span class="inline-flex items-center rounded-full bg-white/20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm">
                            <i class="fas fa-envelope mr-2"></i>
                            {{ profile_owner.email }}
                        </span>
                        <!-- Development Test Button -->
                        <a href="{{ url_for('onboarding', restart='true') }}" class="inline-flex items-center rounded-full bg-white/20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm hover:bg-white/30 transition-colors duration-200">
                            <i class="fas fa-cog mr-2"></i>
                            Test Onboarding
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-8 space-y-6">


            <!-- Recent Submissions -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-history"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Recent Submissions</h2>
                            <p class="text-sm text-gray-500">Your latest answers</p>
                        </div>
                    </div>

                    {% if submissions %}
                    <div class="grid gap-4">
                        {% for submission in submissions %}
                        <a href="{{ url_for('submission_details', submission_id=submission.id) }}"
                           class="block bg-gray-50 rounded-lg p-4 transition-all duration-200 hover:shadow-md hover:bg-white transform hover:-translate-y-1 border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        {{ submission.part.question.description }}
                                    </p>
                                    <div class="flex items-center mt-1">
                                        <span class="text-xs text-gray-500 flex items-center">
                                            <i class="fas fa-puzzle-piece mr-1 text-indigo-400"></i>
                                            Part {{ submission.part.id }}
                                        </span>
                                        <span class="mx-2 text-gray-300">•</span>
                                        <span class="text-xs text-gray-500 flex items-center">
                                            <i class="fas fa-clock mr-1 text-indigo-400"></i>
                                            {{ submission.timestamp.strftime('%b %d, %Y at %H:%M') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4 flex-shrink-0">
                                    <div class="relative w-12 h-12">
                                        <svg class="w-full h-full" viewBox="0 0 36 36">
                                            <path class="stroke-current text-gray-200" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                            <path class="stroke-current {% if submission.score == submission.part.score %}text-green-500{% elif submission.score > 0 %}text-amber-500{% else %}text-red-500{% endif %}" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="{{ (submission.part.score > 0) and ((submission.score / submission.part.score) * 100) or 0 }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                            <text x="18" y="20.5" class="fill-current {% if submission.score == submission.part.score %}text-green-700{% elif submission.score > 0 %}text-amber-700{% else %}text-red-700{% endif %} font-bold text-xs" text-anchor="middle">{{ (submission.part.score > 0) and ((submission.score / submission.part.score) * 100) | int or 0 }}%</text>
                                        </svg>
                                    </div>
                                    <div class="text-xs font-medium text-center {% if submission.score == submission.part.score %}text-green-600{% elif submission.score > 0 %}text-amber-600{% else %}text-red-600{% endif %} mt-1">
                                        {{ submission.score }}/{{ submission.part.score }}
                                    </div>
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="flex flex-col items-center justify-center py-12 bg-gray-50 rounded-lg">
                        <div class="w-16 h-16 rounded-full bg-indigo-50 flex items-center justify-center mb-4">
                            <i class="fas fa-clipboard-list text-indigo-300 text-xl"></i>
                        </div>
                        <h3 class="text-base font-medium text-gray-900 mb-1">No submissions yet</h3>
                        <p class="text-gray-500 text-center max-w-md text-sm">
                            Start solving problems to see your submissions here
                        </p>
                        <a href="{{ url_for('vault') }}"
                           class="mt-4 inline-flex items-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-indigo-500 transition-colors duration-200">
                            <i class="fas fa-book-open mr-2"></i>
                            Go to Question Vault
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-4 space-y-6">
            <!-- Progress Stats -->
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 mr-4">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Progress</h2>
                    </div>

                    <div class="flex flex-col items-center">
                        <!-- Removed Doughnut Chart -->
                        <!-- Display Correct Count centrally -->
                         <div class="relative w-40 h-40 mb-4 flex items-center justify-center flex-col bg-green-50 rounded-full border-4 border-green-100">
                            <span class="text-4xl font-bold text-green-600">{{ correct_count }}</span>
                            <span class="text-sm text-green-500 mt-1">Correct</span>
                        </div>

                        <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 w-full mt-4">
                            <div class="flex flex-col items-center p-3 bg-green-50 rounded-lg text-center">
                                <span class="text-lg font-semibold text-green-600">{{ correct_count }}</span>
                                <span class="text-xs text-gray-500">Correct</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-amber-50 rounded-lg text-center">
                                <span class="text-lg font-semibold text-amber-600">{{ partial_count }}</span>
                                <span class="text-xs text-gray-500">Partial</span>
                            </div>
                             <div class="flex flex-col items-center p-3 bg-red-50 rounded-lg text-center">
                                <span class="text-lg font-semibold text-red-600">{{ incorrect_count }}</span>
                                <span class="text-xs text-gray-500">Incorrect</span>
                            </div>
                            <div class="flex flex-col items-center p-3 bg-gray-50 rounded-lg text-center">
                                <span class="text-lg font-semibold text-gray-600">{{ unattempted_count }}</span>
                                <span class="text-xs text-gray-500">Unattempted</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>





            <!-- Learning Preferences -->
            {% if session.user_id == profile_owner.id and profile_owner.onboarding_completed %}
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-100 text-green-600 mr-4">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Learning Preferences</h2>
                    </div>

                    <div class="space-y-4">
                        <!-- Grade Level -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-school text-gray-500 mr-3"></i>
                                <span class="font-medium text-gray-700">Grade Level</span>
                            </div>
                            <span class="text-indigo-600 font-semibold">{{ profile_owner.grade_level or 'Not set' }}</span>
                        </div>

                        <!-- Subjects -->
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-book text-gray-500 mr-3"></i>
                                <span class="font-medium text-gray-700">Subjects</span>
                            </div>
                            {% if profile_owner.get_subjects_taken() %}
                                <div class="flex flex-wrap gap-2">
                                    {% for subject in profile_owner.get_subjects_taken() %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                            {{ subject }}
                                        </span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <span class="text-gray-500 text-sm">No subjects selected</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Account Management -->
            {% if session.user_id == profile_owner.id %}
            <div class="bg-white rounded-xl shadow-sm ring-1 ring-gray-900/5 overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-red-100 text-red-600 mr-4">
                            <i class="fas fa-user-cog"></i>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-900">Account Management</h2>
                    </div>

                    <div class="flex flex-col items-center space-y-4">
                        <!-- Restart Onboarding Button -->
                        <div class="text-center">
                            <button id="restart-onboarding-btn" class="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors duration-200">
                                <i class="fas fa-redo mr-2"></i>
                                Update Subject & Level
                            </button>
                            <p class="text-xs text-gray-500 mt-2 text-center">Change your grade level or subjects</p>
                        </div>

                        <!-- Delete Account Button -->
                        <div class="text-center">
                            <button id="delete-account-btn" class="inline-flex items-center rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 transition-colors duration-200">
                                <i class="fas fa-user-times mr-2"></i>
                                Delete Account
                            </button>
                            <p class="text-xs text-gray-500 mt-2 text-center">This action cannot be undone. All your data will be permanently deleted.</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
    /* Animated background grid */
    .bg-grid-white {
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    }

    /* Subtle animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Circular progress animation */
    @keyframes progress {
        0% {
            stroke-dasharray: 0 100;
        }
    }

    .stroke-current {
        animation: progress 1s ease-out forwards;
    }


</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add staggered animation to cards
        const cards = document.querySelectorAll('.rounded-xl');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(10px)';
            card.style.transition = 'all 0.3s ease-out';
            card.style.transitionDelay = `${index * 0.1}s`;

            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });

        // Progress Chart - Removed Initialization











        // Restart Onboarding Button Handler
        const restartOnboardingBtn = document.getElementById('restart-onboarding-btn');
        if (restartOnboardingBtn) {
            restartOnboardingBtn.addEventListener('click', function() {
                // Create a modal for confirmation
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
                        <h3 class="text-xl font-bold text-indigo-600 mb-4">Update Subject & Level</h3>
                        <p class="text-gray-700 mb-6">This will reset your current subject and grade level preferences. You'll be able to select new ones. Your progress and submissions will not be affected.</p>
                        <div class="flex justify-end space-x-4">
                            <button id="cancel-restart" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors">Cancel</button>
                            <form action="/restart_onboarding" method="POST">
                                <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">Update Preferences</button>
                            </form>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Add event listener to cancel button
                document.getElementById('cancel-restart').addEventListener('click', function() {
                    document.body.removeChild(modal);
                });

                // Close modal when clicking outside
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
            });
        }

        // Delete Account Button Handler
        const deleteAccountBtn = document.getElementById('delete-account-btn');
        if (deleteAccountBtn) {
            deleteAccountBtn.addEventListener('click', function() {
                // Create a modal for confirmation
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
                        <h3 class="text-xl font-bold text-red-600 mb-4">Delete Account</h3>
                        <p class="text-gray-700 mb-6">Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.</p>
                        <div class="flex justify-end space-x-4">
                            <button id="cancel-delete" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors">Cancel</button>
                            <form action="/delete_account" method="POST">
                                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">Delete Account</button>
                            </form>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Add event listener to cancel button
                document.getElementById('cancel-delete').addEventListener('click', function() {
                    document.body.removeChild(modal);
                });

                // Close modal when clicking outside
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
            });
        }
    });
</script>
{% endblock %}
