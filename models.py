from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import Column, Integer, String, Text, ForeignKey, Table, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import random
import string

db = SQLAlchemy()

# Association table for User <-> Group many-to-many relationship
user_group_association = Table('user_group_association', db.Model.metadata,
    Column('user_id', Integer, ForeignKey('users.id')),
    Column('group_id', Integer, ForeignKey('groups.id'))
)

# Association table for ProblemSet <-> Question many-to-many relationship
problemset_question_association = Table('problemset_question_association', db.Model.metadata,
    Column('problemset_id', Integer, ForeignKey('problemsets.id')),
    Column('question_id', Integer, ForeignKey('questions.id'))
)

# Association table for ProblemSet <-> Group many-to-many relationship
problemset_group_association = Table('problemset_group_association', db.Model.metadata,
    <PERSON>umn('problemset_id', <PERSON><PERSON><PERSON>, Foreign<PERSON>ey('problemsets.id')),
    Column('group_id', Integer, ForeignKey('groups.id'))
)

class Group(db.Model):
    __tablename__ = 'groups'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    owner_id = Column(Integer, ForeignKey('users.id', name='fk_groups_owner_id'), nullable=False) # Added owner_id
    invite_code = Column(String(8), nullable=True)  # 8-character invite code
    members = relationship("User", secondary=user_group_association, back_populates="groups")
    owner = relationship("User", back_populates="owned_groups") # Added relationship to User

    def generate_invite_code(self):
        """Generate a unique 8-character invite code for the group"""
        while True:
            # Generate a random 8-character code with uppercase letters and numbers
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
            # Check if this code already exists
            if not Group.query.filter_by(invite_code=code).first():
                self.invite_code = code
                return code

    def revoke_invite_code(self):
        """Revoke the current invite code"""
        self.invite_code = None


class Subject(db.Model):
    __tablename__ = 'subjects'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    syllabus = db.Column(db.Integer, nullable=False, unique=True)
    topics = db.relationship('Topic', backref='subject', lazy=True)

    def __repr__(self):
        return f"{self.syllabus}: {self.name}"

class Topic(db.Model):
    __tablename__ = 'topics'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    subject_id = db.Column(
        db.Integer,
        db.ForeignKey('subjects.id', name='fk_topics_subject_id'),
        nullable=False
    )
    questions = db.relationship('Question', backref='topic', lazy=True)

    def __repr__(self):
        return f"{self.name}"

class Question(db.Model):
    __tablename__ = 'questions'
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=True)
    description = db.Column(db.Text, nullable=True)
    source = db.Column(db.Text, nullable=True)
    is_dojo = db.Column(db.Boolean, nullable=False, default=False)
    topic_id = db.Column(
        db.Integer,
        db.ForeignKey('topics.id', name='fk_questions_topic_id'),
        nullable=True
    )
    parts = db.relationship('Part', backref='question', lazy=True)
    attachment = db.Column(db.String(255))

    # Prerequisite relationships
    prerequisites = db.relationship(
        'QuestionPrerequisite',
        foreign_keys='QuestionPrerequisite.question_id',
        backref='question',
        lazy='select',
        cascade='all, delete-orphan'
    )

    prerequisite_for = db.relationship(
        'QuestionPrerequisite',
        foreign_keys='QuestionPrerequisite.prerequisite_question_id',
        backref='prerequisite_question',
        lazy='select'
    )

    # Relevance relationships
    relevant_questions = db.relationship(
        'QuestionRelevance',
        foreign_keys='QuestionRelevance.question_id',
        backref='question',
        lazy='select',
        cascade='all, delete-orphan'
    )

    relevant_to = db.relationship(
        'QuestionRelevance',
        foreign_keys='QuestionRelevance.relevant_question_id',
        backref='relevant_question',
        lazy='select'
    )

    # Notes relevance relationships
    relevant_notes = db.relationship(
        'QuestionNotesRelevance',
        foreign_keys='QuestionNotesRelevance.question_id',
        backref='question',
        lazy='select',
        cascade='all, delete-orphan'
    )

    def __repr__(self):
        return f"{self.title}:{self.description}"

class QuestionPrerequisite(db.Model):
    __tablename__ = 'question_prerequisites'
    id = db.Column(db.Integer, primary_key=True)
    question_id = db.Column(
        db.Integer,
        db.ForeignKey('questions.id', name='fk_prerequisites_question_id'),
        nullable=False
    )
    prerequisite_question_id = db.Column(
        db.Integer,
        db.ForeignKey('questions.id', name='fk_prerequisites_prerequisite_id'),
        nullable=False
    )
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())

    # Ensure a question can't be a prerequisite of itself and no duplicate prerequisites
    __table_args__ = (
        db.CheckConstraint('question_id != prerequisite_question_id', name='no_self_prerequisite'),
        db.UniqueConstraint('question_id', 'prerequisite_question_id', name='unique_prerequisite'),
    )

    def __repr__(self):
        return f"QuestionPrerequisite(question={self.question_id}, prerequisite={self.prerequisite_question_id})"

class QuestionRelevance(db.Model):
    __tablename__ = 'question_relevance'
    id = db.Column(db.Integer, primary_key=True)
    question_id = db.Column(
        db.Integer,
        db.ForeignKey('questions.id', name='fk_relevance_question_id'),
        nullable=False
    )
    relevant_question_id = db.Column(
        db.Integer,
        db.ForeignKey('questions.id', name='fk_relevance_relevant_id'),
        nullable=False
    )
    relevance_type = db.Column(db.String(50), nullable=False, default='general')  # general, similar_concept, related_topic, etc.
    strength = db.Column(db.Integer, nullable=False, default=1)  # 1-5 scale for relevance strength
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())

    # Ensure a question can't be relevant to itself and no duplicate relevance relationships
    __table_args__ = (
        db.CheckConstraint('question_id != relevant_question_id', name='no_self_relevance'),
        db.UniqueConstraint('question_id', 'relevant_question_id', name='unique_relevance'),
    )

    def __repr__(self):
        return f"QuestionRelevance(question={self.question_id}, relevant={self.relevant_question_id}, type={self.relevance_type})"

class QuestionNotesRelevance(db.Model):
    __tablename__ = 'question_notes_relevance'
    id = db.Column(db.Integer, primary_key=True)
    question_id = db.Column(
        db.Integer,
        db.ForeignKey('questions.id', name='fk_notes_relevance_question_id'),
        nullable=False
    )
    notes_chunk_id = db.Column(
        db.Integer,
        db.ForeignKey('notes_chunks.id', name='fk_notes_relevance_chunk_id'),
        nullable=False
    )
    relevance_type = db.Column(db.String(50), nullable=False, default='general')  # general, concept_explanation, worked_example, etc.
    strength = db.Column(db.Integer, nullable=False, default=1)  # 1-5 scale for relevance strength
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())

    # Ensure no duplicate relevance relationships
    __table_args__ = (
        db.UniqueConstraint('question_id', 'notes_chunk_id', name='unique_notes_relevance'),
    )

    def __repr__(self):
        return f"QuestionNotesRelevance(question={self.question_id}, chunk={self.notes_chunk_id}, type={self.relevance_type})"

class Part(db.Model):
    __tablename__ = 'parts'
    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.Text, nullable=False)
    answer = db.Column(db.Text, nullable=True)
    score = db.Column(db.Integer, nullable=False, default=0)
    question_id = db.Column(
        db.Integer,
        db.ForeignKey('questions.id', name='fk_parts_question_id'),
        nullable=False
    )
    attachments = db.relationship('Attachment', backref='part', lazy=True)
    input_type = db.Column(db.Text, nullable=True, default="saq") # saq / mcq / image
    options = db.relationship('Option', backref='part', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f"{self.description}"

class Option(db.Model):
    __tablename__ = 'options'
    id = db.Column(db.Integer, primary_key=True)
    part_id = db.Column(
        db.Integer,
        db.ForeignKey('parts.id', name='fk_options_part_id'),
        nullable=False
    )
    description = db.Column(db.Text, nullable=False)
    is_correct = db.Column(db.Boolean, nullable=False, default=False)

    def __repr__(self):
        return f"{self.description}: {self.is_correct}"

class Attachment(db.Model):
    __tablename__ = 'attachments'
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=True)
    part_id = db.Column(
        db.Integer,
        db.ForeignKey('parts.id', name='fk_attachments_part_id'),
        nullable=False
    )

    def __repr__(self):
        return f"{self.filename} attached to part {self.part_id}"

class User(db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), nullable=False, unique=True)
    email = db.Column(db.String(120), nullable=False, unique=True)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='member')
    submissions = db.relationship('Submission', backref='user', lazy=True)
    last_active = db.Column(db.DateTime,nullable=True, default=db.func.current_timestamp())
    daily_time_goal = db.Column(db.Integer, nullable=True, default=3600)  # Default goal: 1 hour (in seconds)
    groups = relationship("Group", secondary=user_group_association, back_populates="members")
    owned_groups = relationship("Group", back_populates="owner", lazy='dynamic') # Added relationship for owned groups

    # Clarification relationships
    sent_clarifications = db.relationship('Clarification', foreign_keys='Clarification.student_id', lazy='dynamic')
    answered_clarifications = db.relationship('Clarification', foreign_keys='Clarification.teacher_id', lazy='dynamic')

    # Onboarding fields
    onboarding_completed = db.Column(db.Boolean, nullable=True, default=False)
    grade_level = db.Column(db.String(10), nullable=True)  # 'PSLE', 'J1', 'J2'
    subjects_taken = db.Column(db.Text, nullable=True)  # JSON string of selected subjects
    subject_confidence = db.Column(db.Text, nullable=True)  # JSON string of confidence levels
    chemistry_topics_struggle = db.Column(db.Text, nullable=True)  # JSON string of chemistry topics user struggles with
    onboarding_completed_at = db.Column(db.DateTime, nullable=True)

    def __repr__(self):
        return f"{self.username}"

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_first_login_of_day(self):
        """Check if this is the first login of the day for the user"""
        from datetime import datetime, date
        today = date.today()

        # If last_active is None, this is the first login ever
        if self.last_active is None:
            return True

        # Check if the last_active date is different from today
        return self.last_active.date() < today

    def update_login_date(self):
        """Update the last login date to now"""
        from datetime import datetime
        self.last_active = datetime.now()

    @property
    def is_admin(self):
        return self.role == 'admin'

    def get_subjects_taken(self):
        """Get subjects taken as a list"""
        if self.subjects_taken:
            import json
            try:
                return json.loads(self.subjects_taken)
            except (json.JSONDecodeError, TypeError):
                return []
        return []

    def set_subjects_taken(self, subjects_list):
        """Set subjects taken from a list"""
        import json
        self.subjects_taken = json.dumps(subjects_list) if subjects_list else None

    def get_subject_confidence(self):
        """Get subject confidence as a dictionary"""
        if self.subject_confidence:
            import json
            try:
                return json.loads(self.subject_confidence)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}

    def set_subject_confidence(self, confidence_dict):
        """Set subject confidence from a dictionary"""
        import json
        self.subject_confidence = json.dumps(confidence_dict) if confidence_dict else None

    def complete_onboarding(self, grade_level, subjects_taken, subject_confidence):
        """Complete the onboarding process"""
        from datetime import datetime
        self.grade_level = grade_level
        self.set_subjects_taken(subjects_taken)
        self.set_subject_confidence(subject_confidence)
        self.onboarding_completed = True
        self.onboarding_completed_at = datetime.now()

class DailyActivity(db.Model):
    __tablename__ = 'daily_activity'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(
        db.Integer,
        db.ForeignKey('users.id', name='fk_daily_activity_user_id'),
        nullable=False
    )
    date = db.Column(db.Date, nullable=False)
    activity_count = db.Column(db.Integer, nullable=False, default=0)

    __table_args__ = (
        db.UniqueConstraint('user_id', 'date', name='unique_user_date'),
    )

    def __repr__(self):
        return f"Activity {self.date} - {self.activity_count}"

class DailyActiveTime(db.Model):
    __tablename__ = 'daily_active_time'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_daily_active_time_user_id'), nullable=False)
    date = db.Column(db.Date, nullable=False, default=db.func.current_date())
    active_time = db.Column(db.Integer, default=0)  # Time in seconds

    __table_args__ = (
        db.UniqueConstraint('user_id', 'date', name='unique_user_date_active_time'),
    )

    def __repr__(self):
        return f"DailyActiveTime(user_id={self.user_id}, date={self.date}, active_time={self.active_time})"

class Submission(db.Model):
    __tablename__ = 'submissions'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    part_id = db.Column(db.Integer, db.ForeignKey('parts.id'), nullable=False)
    answer = db.Column(db.Text, nullable=False)
    score = db.Column(db.Float, nullable=True)
    timestamp = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())

    # Relationships
    question = db.relationship("Question", backref="submissions")
    part = db.relationship("Part", backref="submissions")

    def __repr__(self):
        return f"Submission by User {self.user_id} for Question {self.question_id}"

    def validate(self):
        """Validate submission data"""
        if not self.answer or not self.answer.strip():
            raise ValueError("Answer cannot be empty")
        if not self.user_id or not self.question_id or not self.part_id:
            raise ValueError("Missing required submission data")

class ProblemSet(db.Model):
    __tablename__ = 'problemsets'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    created_by = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_problemsets_user_id'), nullable=False)
    pdf_attachment = db.Column(db.String(255), nullable=True)  # Store the filename of the PDF attachment

    # Relationships
    questions = relationship("Question", secondary=problemset_question_association, backref="problemsets")
    shared_with = relationship("Group", secondary=problemset_group_association, backref="problemsets")
    creator = relationship("User", backref="created_problemsets")
    submissions = relationship("ProblemSetSubmission", back_populates="problemset", lazy=True)

    def __repr__(self):
        return f"ProblemSet: {self.name}"

    def validate(self):
        """Validate problemset data"""
        if not self.name or not self.name.strip():
            raise ValueError("Problem set name cannot be empty")
        if not self.created_by:
            raise ValueError("Problem set must have a creator")
        if not self.questions:
            raise ValueError("Problem set must contain at least one question")

class ProblemSetSubmission(db.Model):
    __tablename__ = 'problemset_submissions'
    id = db.Column(db.Integer, primary_key=True)
    problemset_id = db.Column(db.Integer, db.ForeignKey('problemsets.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    submitted_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    total_score = db.Column(db.Integer, nullable=False, default=0)
    max_score = db.Column(db.Integer, nullable=False, default=0)
    completion_time = db.Column(db.Integer, nullable=True)  # Time taken in seconds
    status = db.Column(db.String(20), nullable=False, default='in_progress')

    # Relationships
    user = db.relationship("User", backref="problemset_submissions")
    problemset = db.relationship("ProblemSet", back_populates="submissions")
    question_submissions = db.relationship("Submission",
        secondary="problemset_submission_questions",
        backref=db.backref("problemset_submissions", lazy=True))

    def __repr__(self):
        return f"ProblemSet Submission by {self.user.username} for {self.problemset.name}"

    def calculate_scores(self):
        """Calculate total and max scores from question submissions"""
        self.total_score = sum(sub.score for sub in self.question_submissions)
        self.max_score = sum(sub.part.score for sub in self.question_submissions)
        return self.total_score, self.max_score

    def validate(self):
        """Validate submission data"""
        if not self.problemset_id or not self.user_id:
            raise ValueError("Missing required submission data")
        if self.total_score < 0:
            raise ValueError("Total score cannot be negative")
        if self.max_score < 0:
            raise ValueError("Max score cannot be negative")
        if self.completion_time and self.completion_time < 0:
            raise ValueError("Completion time cannot be negative")

# Association table for ProblemSetSubmission <-> Submission many-to-many relationship
problemset_submission_questions = db.Table('problemset_submission_questions',
    db.Column('problemset_submission_id', db.Integer, db.ForeignKey('problemset_submissions.id'), primary_key=True),
    db.Column('submission_id', db.Integer, db.ForeignKey('submissions.id'), primary_key=True)
)

class IncompleteSubmission(db.Model):
    __tablename__ = 'incomplete_submissions'
    id = db.Column(db.Integer, primary_key=True)
    problemset_id = db.Column(db.Integer, db.ForeignKey('problemsets.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    part_id = db.Column(db.Integer, db.ForeignKey('parts.id'), nullable=False)
    answer = db.Column(db.Text, nullable=True)
    last_updated = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # Relationships
    user = db.relationship("User", backref="incomplete_submissions")
    problemset = db.relationship("ProblemSet", backref="incomplete_submissions")
    question = db.relationship("Question", backref="incomplete_submissions")
    part = db.relationship("Part", backref="incomplete_submissions")

    def __repr__(self):
        return f"Incomplete Submission by {self.user.username} for {self.problemset.name}"

    def validate(self):
        """Validate incomplete submission data"""
        if not self.problemset_id or not self.user_id or not self.question_id or not self.part_id:
            raise ValueError("Missing required submission data")
        if self.answer is not None and not isinstance(self.answer, str):
            raise ValueError("Answer must be a string")

    @classmethod
    def get_or_create(cls, problemset_id, user_id, question_id, part_id):
        """Get existing incomplete submission or create a new one with retry logic for database locks"""
        from sqlalchemy.exc import OperationalError
        import time

        max_retries = 3
        retry_delay = 0.1  # seconds

        for attempt in range(max_retries):
            try:
                # Try to find an existing submission
                submission = cls.query.filter_by(
                    problemset_id=problemset_id,
                    user_id=user_id,
                    question_id=question_id,
                    part_id=part_id
                ).first()

                # If no submission exists, create a new one
                if not submission:
                    submission = cls(
                        problemset_id=problemset_id,
                        user_id=user_id,
                        question_id=question_id,
                        part_id=part_id
                    )
                    db.session.add(submission)
                    db.session.commit()

                return submission

            except OperationalError as e:
                # Handle database locks specifically
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    db.session.rollback()
                    time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    db.session.rollback()
                    # Re-raise the exception after max retries
                    raise
            except Exception as e:
                db.session.rollback()
                # Re-raise any other exceptions
                raise

class MarkingPoint(db.Model):
    __tablename__ = 'marking_points'
    id = db.Column(db.Integer, primary_key=True)
    part_id = db.Column(
        db.Integer,
        db.ForeignKey('parts.id', name='fk_marking_points_part_id'),
        nullable=False
    )
    description = db.Column(db.Text, nullable=False)
    score = db.Column(db.Float, nullable=False)
    order = db.Column(db.Integer, nullable=False)  # To maintain the order of marking points
    is_auto_generated = db.Column(db.Boolean, default=False)  # To track if this was generated by API
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # Relationship
    part = db.relationship('Part', backref=db.backref('marking_points', lazy=True, order_by=order))

    def __repr__(self):
        return f"Marking Point: {self.description} ({self.score} points)"

    def validate(self):
        """Validate marking point data"""
        if not self.description or not self.description.strip():
            raise ValueError("Description cannot be empty")
        if self.score < 0:
            raise ValueError("Score cannot be negative")
        if self.order < 0:
            raise ValueError("Order cannot be negative")

class Post(db.Model):
    """Model for storing activity feed posts/milestones."""
    __tablename__ = 'posts'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', name='fk_posts_user_id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('groups.id', name='fk_posts_group_id'), nullable=False)
    post_type = db.Column(db.String(50), nullable=False)  # goal_achieved, streak_milestone, first_activity, etc.
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=True)
    data = db.Column(db.Text, nullable=True)  # JSON data for additional information
    timestamp = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())

    # Relationships
    user = db.relationship("User", backref=db.backref("posts", lazy=True))
    group = db.relationship("Group", backref=db.backref("posts", lazy=True))

    def __repr__(self):
        return f"Post({self.post_type}): {self.title} by {self.user.username}"


class ReviewSchedule(db.Model):
    """Track spaced repetition data for each user-problem combination"""
    __tablename__ = 'review_schedule'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    part_id = db.Column(db.Integer, db.ForeignKey('parts.id'), nullable=False)
    ease_factor = db.Column(db.Float, nullable=False, default=2.5)  # SM-2 algorithm ease factor
    repetition_number = db.Column(db.Integer, nullable=False, default=0)
    interval_days = db.Column(db.Integer, nullable=False, default=1)
    next_review_date = db.Column(db.Date, nullable=False)
    last_reviewed = db.Column(db.Date, nullable=True)
    quality_score = db.Column(db.Integer, nullable=True)  # 0-5 based on last performance
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # Relationships
    user = db.relationship("User", backref="review_schedules")
    question = db.relationship("Question", backref="review_schedules")
    part = db.relationship("Part", backref="review_schedules")

    # Unique constraint to prevent duplicate schedules
    __table_args__ = (
        db.UniqueConstraint('user_id', 'question_id', 'part_id', name='unique_user_question_part_review'),
    )

    def __repr__(self):
        return f"ReviewSchedule(user={self.user_id}, question={self.question_id}, part={self.part_id}, next_review={self.next_review_date})"


class PerformanceAnalytics(db.Model):
    """Track user performance analytics by topic and subject"""
    __tablename__ = 'performance_analytics'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    topic_id = db.Column(db.Integer, db.ForeignKey('topics.id'), nullable=True)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'), nullable=True)
    success_rate = db.Column(db.Float, nullable=False, default=0.0)  # Percentage (0.0 to 1.0)
    total_attempts = db.Column(db.Integer, nullable=False, default=0)
    correct_attempts = db.Column(db.Integer, nullable=False, default=0)
    average_score = db.Column(db.Float, nullable=False, default=0.0)
    learning_velocity = db.Column(db.Float, nullable=False, default=0.0)  # Rate of improvement
    last_calculated = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())

    # Relationships
    user = db.relationship("User", backref="performance_analytics")
    topic = db.relationship("Topic", backref="performance_analytics")
    subject = db.relationship("Subject", backref="performance_analytics")

    # Unique constraint for user-topic and user-subject combinations
    __table_args__ = (
        db.UniqueConstraint('user_id', 'topic_id', name='unique_user_topic_analytics'),
        db.UniqueConstraint('user_id', 'subject_id', name='unique_user_subject_analytics'),
    )

    def __repr__(self):
        if self.topic_id:
            return f"PerformanceAnalytics(user={self.user_id}, topic={self.topic_id}, success_rate={self.success_rate:.2f})"
        else:
            return f"PerformanceAnalytics(user={self.user_id}, subject={self.subject_id}, success_rate={self.success_rate:.2f})"


class RecommendationHistory(db.Model):
    """Track recommendation history and effectiveness"""
    __tablename__ = 'recommendation_history'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    part_id = db.Column(db.Integer, db.ForeignKey('parts.id'), nullable=False)
    recommendation_type = db.Column(db.String(50), nullable=False)  # 'critical', 'reinforcement', 'mastery', 'new'
    recommendation_reason = db.Column(db.Text, nullable=True)  # Explanation for the recommendation
    priority_score = db.Column(db.Float, nullable=False, default=0.0)  # Algorithm-calculated priority


class Clarification(db.Model):
    """Model for storing student clarification requests to teachers"""
    __tablename__ = 'clarifications'
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    part_id = db.Column(db.Integer, db.ForeignKey('parts.id'), nullable=True)  # Optional, for part-specific clarifications
    subject = db.Column(db.String(255), nullable=False)  # Subject line for the clarification
    message = db.Column(db.Text, nullable=False)  # Student's clarification request
    context_data = db.Column(db.Text, nullable=True)  # JSON data with question context (user's answer, etc.)
    status = db.Column(db.String(20), nullable=False, default='pending')  # 'pending', 'answered', 'closed'
    teacher_response = db.Column(db.Text, nullable=True)  # Teacher's response
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Teacher who responded
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # Relationships
    student = db.relationship("User", foreign_keys=[student_id])
    teacher = db.relationship("User", foreign_keys=[teacher_id])
    question = db.relationship("Question", backref="clarifications")
    part = db.relationship("Part", backref="clarifications")

    def __repr__(self):
        return f"Clarification {self.id} from User {self.student_id} for Question {self.question_id}"


class NotesChunk(db.Model):
    """Model for storing chunked chemistry notes sections."""
    __tablename__ = 'notes_chunks'
    id = db.Column(db.Integer, primary_key=True)

    # Content
    content = db.Column(db.Text, nullable=False)
    title = db.Column(db.String(500), nullable=False)

    # File metadata
    filename = db.Column(db.String(255), nullable=False)
    chapter_id = db.Column(db.String(255), nullable=False)
    section_id = db.Column(db.String(255), nullable=False)

    # Hierarchy
    level = db.Column(db.Integer, nullable=False)  # Header level (1-6)
    parent_sections = db.Column(db.Text, nullable=True)  # JSON array of parent section titles

    # Position in file
    start_line = db.Column(db.Integer, nullable=False)
    end_line = db.Column(db.Integer, nullable=False)

    # Statistics
    word_count = db.Column(db.Integer, nullable=False)
    char_count = db.Column(db.Integer, nullable=False)

    # Metadata
    chunk_hash = db.Column(db.String(64), nullable=False, unique=True)  # SHA256 hash for deduplication
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # Relationships
    embeddings = db.relationship('NotesEmbedding', backref='chunk', lazy=True, cascade='all, delete-orphan')

    # Question relevance relationships
    question_relevance = db.relationship(
        'QuestionNotesRelevance',
        foreign_keys='QuestionNotesRelevance.notes_chunk_id',
        backref='notes_chunk',
        lazy='select'
    )

    def __repr__(self):
        return f'<NotesChunk {self.id}: {self.title[:50]}...>'

    def to_dict(self):
        """Convert chunk to dictionary for JSON serialization."""
        import json
        return {
            'id': self.id,
            'content': self.content,
            'title': self.title,
            'filename': self.filename,
            'chapter_id': self.chapter_id,
            'section_id': self.section_id,
            'level': self.level,
            'parent_sections': json.loads(self.parent_sections) if self.parent_sections else [],
            'start_line': self.start_line,
            'end_line': self.end_line,
            'word_count': self.word_count,
            'char_count': self.char_count,
            'chunk_hash': self.chunk_hash,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class NotesEmbedding(db.Model):
    """Model for storing embeddings of notes chunks."""
    __tablename__ = 'notes_embeddings'
    id = db.Column(db.Integer, primary_key=True)

    # Foreign key to chunk
    chunk_id = db.Column(db.Integer, db.ForeignKey('notes_chunks.id', name='fk_notes_embeddings_chunk_id'), nullable=False)

    # Embedding data
    model_name = db.Column(db.String(100), nullable=False)  # e.g., 'all-MiniLM-L6-v2'
    embedding_vector = db.Column(db.Text, nullable=False)  # JSON array of floats
    vector_dimension = db.Column(db.Integer, nullable=False)

    # Metadata
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())

    def __repr__(self):
        return f'<NotesEmbedding {self.id}: {self.model_name}>'

    def get_vector(self):
        """Get embedding vector as numpy array."""
        import json
        import numpy as np
        return np.array(json.loads(self.embedding_vector))

    def set_vector(self, vector):
        """Set embedding vector from numpy array."""
        import json
        import numpy as np
        if isinstance(vector, np.ndarray):
            vector = vector.tolist()
        self.embedding_vector = json.dumps(vector)
        self.vector_dimension = len(vector)
