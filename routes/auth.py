import os
import secrets
import string
from supabase import create_client, Client
from flask import request, render_template, redirect, url_for, flash, session
from gotrue.errors import AuthApiError
from models import User, db
from .utils import login_required, app_logger, user_logger, error_logger, update_user_activity

def load_admin_emails():
    """Load admin emails from admin.txt file."""
    admin_emails = set()
    try:
        if os.path.exists('admin.txt'):
            with open('admin.txt', 'r') as f:
                for line in f:
                    email = line.strip().lower()
                    # Skip empty lines, comments, and invalid emails
                    if email and not email.startswith('#') and '@' in email:
                        admin_emails.add(email)
            app_logger.info(f"Loaded {len(admin_emails)} admin emails from admin.txt")
        else:
            app_logger.warning("admin.txt file not found")
    except Exception as e:
        error_logger.error(f"Error loading admin emails: {str(e)}")
    return admin_emails


def validate_email(email, admin_emails):
    if not email:
        return False

    email = email.strip().lower()

    # Allow .edu.sg emails or admin emails
    # if email.endswith('.edu.sg') or email in admin_emails:
    #     return True

    return True

    return False


def generate_random_password(length=12):
    """Generate a random password for storing in local database."""
    characters = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(characters) for _ in range(length))


def get_username_from_email(email):
    """Extract username from email (part before @)."""
    return email.split('@')[0]


def register_auth_routes(app, db, session, supabase_client):
    """Register authentication routes using email/password with OTP verification for registration."""

    # Load admin emails
    admin_emails = load_admin_emails()

    @app.route("/login", methods=['GET', 'POST'])
    def login():
        """Handle login with email OTP verification."""
        # Redirect if already logged in
        if 'user_id' in session:
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()
            otp = request.form.get('otp', '').strip()

            # Validate email format
            if not validate_email(email, admin_emails):
                flash('Please use a valid .edu.sg email address.', 'error')
                return render_template('login.html')

            if not otp:
                # Step 1: Send OTP via Supabase
                try:
                    response = supabase_client.auth.sign_in_with_otp({
                        "email": email,
                        "options": {
                            "should_create_user": False  # Don't create user if they don't exist
                        }
                    })

                    app_logger.info(f"OTP sent to {email} for login")
                    flash('A verification code has been sent to your email.', 'success')
                    return render_template('login.html', otp_sent=True, email=email)

                except Exception as e:
                    error_logger.error(f"Error sending OTP to {email}: {str(e)}")
                    error_message = str(e)

                    # Check for cooldown error from Supabase
                    if "For security purposes, you can only request this after" in error_message:
                        # Extract the cooldown time if possible
                        import re
                        cooldown_match = re.search(r'after (\d+) seconds', error_message)
                        cooldown_seconds = int(cooldown_match.group(1)) if cooldown_match else 14

                        flash(f'Please wait {cooldown_seconds} seconds before requesting another verification code.', 'warning')
                        return render_template('login.html', cooldown_seconds=cooldown_seconds, email=email)

                    # Check if user exists locally - if not, suggest registration
                    user = User.query.filter_by(email=email).first()
                    if not user:
                        flash('No account found with this email. Please register first.', 'error')
                        return redirect(url_for('register'))
                    else:
                        flash('Error sending verification code. Please try again.', 'error')
                        return render_template('login.html')
            else:
                # Step 2: Verify OTP and login
                try:
                    response = supabase_client.auth.verify_otp({
                        "email": email,
                        "token": otp,
                        "type": "email"
                    })

                    if response and response.user:
                        # Check if user exists locally
                        user = User.query.filter_by(email=email).first()

                        if not user:
                            flash('No account found. Please register first.', 'error')
                            return redirect(url_for('register'))

                        # Successful login
                        session['user_id'] = user.id
                        session['username'] = user.username
                        session.permanent = True

                        # Check if this is the first login of the day
                        first_login_of_day = user.is_first_login_of_day()
                        if first_login_of_day:
                            session['show_confetti'] = True
                            user.update_login_date()
                            db.session.commit()
                            app_logger.info(f"First login of the day for user {user.username}")

                        update_user_activity(user.id)
                        user_logger.info(f"User {user.username} logged in successfully")
                        flash('Login successful!', 'success')

                        # Check if user needs onboarding
                        if not user.onboarding_completed:
                            return redirect(url_for('onboarding'))

                        # Redirect to intended page or dashboard
                        next_page = request.args.get('next')
                        if next_page and next_page.startswith('/'):
                            return redirect(next_page)
                        return redirect(url_for('dashboard'))
                    else:
                        flash('Invalid verification code. Please try again.', 'error')
                        return render_template('login.html', otp_sent=True, email=email)

                except Exception as e:
                    error_logger.error(f"Error verifying OTP for {email}: {str(e)}")
                    flash('Invalid verification code. Please try again.', 'error')
                    return render_template('login.html', otp_sent=True, email=email)

        # GET request - show login form
        return render_template('login.html')

    @app.route("/logout")
    @login_required
    def logout():
        """Handle user logout."""
        user_id = session.get('user_id')
        username = session.get('username', 'Unknown')

        # Update activity one last time before logout
        if user_id:
            update_user_activity(user_id)

        try:
            # Sign out from Supabase
            supabase_client.auth.sign_out()
            app_logger.info(f"User {username} signed out from Supabase")
        except Exception as e:
            error_logger.warning(f"Error signing out from Supabase: {str(e)}")

        # Clear the session
        session.clear()

        user_logger.info(f"User {username} (ID: {user_id}) logged out")
        flash("You have been logged out.", "info")
        return redirect(url_for('login'))
    
    @app.route("/register", methods=['GET', 'POST'])
    def register():
        """Handle user registration with email OTP verification."""
        # Redirect if already logged in
        if 'user_id' in session:
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()
            otp = request.form.get('otp', '').strip()

            # Validate email format
            if not validate_email(email, admin_emails):
                flash('Please use a valid .edu.sg email address.', 'error')
                return render_template('register.html')

            if not otp:
                # Step 1: Check if user already exists
                existing_user = User.query.filter_by(email=email).first()
                if existing_user:
                    print(existing_user.name, existing_user.id)
                    flash('An account with this email already exists. Please login instead.', 'error')
                    return redirect(url_for('login'))

                # Send OTP via Supabase
                try:
                    response = supabase_client.auth.sign_in_with_otp({
                        "email": email,
                        "options": {
                            "should_create_user": True  # Create user in Supabase if they don't exist
                        }
                    })

                    app_logger.info(f"OTP sent to {email} for registration")
                    flash('A verification code has been sent to your email.', 'success')
                    return render_template('register.html', otp_sent=True, email=email)

                except Exception as e:
                    error_logger.error(f"Error sending OTP to {email}: {str(e)}")
                    error_message = str(e)

                    # Check for cooldown error from Supabase
                    if "For security purposes, you can only request this after" in error_message:
                        # Extract the cooldown time if possible
                        import re
                        cooldown_match = re.search(r'after (\d+) seconds', error_message)
                        cooldown_seconds = int(cooldown_match.group(1)) if cooldown_match else 14

                        flash(f'Please wait {cooldown_seconds} seconds before requesting another verification code.', 'warning')
                        return render_template('register.html', cooldown_seconds=cooldown_seconds, email=email)

                    flash('Error sending verification code. Please try again.', 'error')
                    return render_template('register.html')
            else:
                # Step 2: Verify OTP and create local user
                try:
                    response = supabase_client.auth.verify_otp({
                        "email": email,
                        "token": otp,
                        "type": "email"
                    })

                    if response and response.user:
                        # Check if user already exists locally (double-check)
                        existing_user = User.query.filter_by(email=email).first()
                        if existing_user:
                            flash('Account already exists. Please login instead.', 'error')
                            return redirect(url_for('login'))

                        # Create new user in local database
                        username = get_username_from_email(email)
                        random_password = generate_random_password()

                        # Ensure username is unique
                        counter = 1
                        original_username = username
                        while User.query.filter_by(username=username).first():
                            username = f"{original_username}{counter}"
                            counter += 1

                        new_user = User(
                            username=username,
                            email=email,
                            role='admin' if email in admin_emails else 'member'
                        )
                        new_user.set_password(random_password)

                        try:
                            db.session.add(new_user)
                            db.session.commit()

                            # Log the user in immediately
                            session['user_id'] = new_user.id
                            session['username'] = new_user.username
                            session.permanent = True

                            update_user_activity(new_user.id)
                            user_logger.info(f"New user {new_user.username} registered and logged in")
                            app_logger.info(f"User {new_user.username} created with email {email}")

                            flash('Account created successfully! Welcome to Vast.', 'success')

                            # Redirect to onboarding
                            return redirect(url_for('onboarding'))

                        except Exception as e:
                            db.session.rollback()
                            error_logger.error(f"Error creating user in database: {str(e)}")
                            flash('Error creating account. Please try again.', 'error')
                            return render_template('register.html')
                    else:
                        flash('Invalid verification code. Please try again.', 'error')
                        return render_template('register.html', otp_sent=True, email=email)

                except Exception as e:
                    error_logger.error(f"Error verifying OTP for {email}: {str(e)}")
                    flash('Invalid verification code. Please try again.', 'error')
                    return render_template('register.html', otp_sent=True, email=email)

        # GET request - show registration form
        return render_template('register.html')
